"""
🔊 Audio Output Thread
Ultra-low latency audio playback with <64ms latency
"""

import numpy as np
import time
import threading
from typing import Optional, Dict, List, Any, Deque
import logging
from collections import deque
import queue

try:
    import pyaudio
    PYAUDIO_AVAILABLE = True
except ImportError:
    PYAUDIO_AVAILABLE = False
    print(" PyAudio not available. Install with: pip install pyaudio")

from .threading_infrastructure import (
    HighPriorityThread,
    LockFreeQueue,
    ProcessingResult
)

logger = logging.getLogger(__name__)

class AudioOutputThread(HighPriorityThread):
    """
    Highest priority thread for ultra-low latency audio playback
    - Real-time audio streaming
    - Smart buffer management
    - Interruption support
    - Multiple format support
    - Performance optimization for <64ms latency
    """
    
    def __init__(self,
                 input_queue: LockFreeQueue,
                 sample_rate: int = 48000,
                 channels: int = 1,
                 chunk_size: Optional[int] = None,
                 device_index: Optional[int] = None,
                 buffer_size: float = 3.0):  # 3 second buffer for longer TTS responses
        
        super().__init__("AudioOutput", priority="highest", target_fps=3000)
        
        # Audio configuration
        self.sample_rate = sample_rate
        self.channels = channels
        self.chunk_size = chunk_size or self._calculate_optimal_chunk_size()
        self.device_index = device_index
        self.buffer_size = buffer_size
        
        # Input queue
        self.input_queue = input_queue
        
        # Audio components
        self.pa = None
        self.output_stream = None
        
        # Playback management
        self.playback_buffer = PlaybackBuffer(
            sample_rate=sample_rate,
            max_duration=buffer_size
        )
        
        # State management
        self.is_playing = False
        self.running = threading.Event()
        self.shutdown_event = threading.Event()
        self.is_interrupted = False
        self.current_audio_info = None
        
        # Performance tracking
        self.audio_chunks_played = 0
        self.total_audio_played = 0.0
        self.underruns = 0
        self.overruns = 0
        self.callback_times = []
        
        # Volume and effects
        self.volume = 1.0
        self.muted = False
        
        # Interruption handling
        self.interruption_fade_samples = int(0.01 * sample_rate)  # 10ms fade
        
    def _calculate_optimal_chunk_size(self) -> int:
        """Calculate optimal chunk size for low latency"""
        # Target ~16ms chunks for minimal latency
        target_latency_ms = 16.0
        chunk_size = int(self.sample_rate * target_latency_ms / 1000.0)
        
        # Round to nearest power of 2 for efficiency
        chunk_size = 2 ** int(np.log2(chunk_size))
        
        logger.info(f" Optimal output chunk size: {chunk_size} samples ({chunk_size/self.sample_rate*1000:.1f}ms)")
        return chunk_size

    def initialize(self) -> bool:
        """Initialize audio output (required by HighPriorityThread)"""
        return self.initialize_audio_output()

    def initialize_audio_output(self) -> bool:
        """Initialize PyAudio output system"""
        if not PYAUDIO_AVAILABLE:
            logger.error(" PyAudio not available - cannot output audio")
            return False
        
        try:
            # Initialize PyAudio
            self.pa = pyaudio.PyAudio()
            
            # Log available output devices
            self._log_output_devices()
            
            # Auto-detect best output device if not specified
            if self.device_index is None:
                self.device_index = self._find_best_output_device()
            
            # Create output stream
            self._create_output_stream()
            
            logger.info(f"Audio output initialized:")
            logger.info(f"   Device: {self.device_index}")
            logger.info(f"   Sample Rate: {self.sample_rate} Hz")
            logger.info(f"   Chunk Size: {self.chunk_size} samples ({self.chunk_size/self.sample_rate*1000:.1f}ms)")
            logger.info(f"   Channels: {self.channels}")
            
            return True
            
        except Exception as e:
            logger.error(f" Failed to initialize audio output: {e}")
            return False
    
    def _log_output_devices(self):
        """Log available audio output devices"""
        logger.info("Available audio output devices:")
        device_count = self.pa.get_device_count()
        
        for i in range(device_count):
            try:
                device_info = self.pa.get_device_info_by_index(i)
                if device_info['maxOutputChannels'] > 0:
                    logger.info(f"   {i}: {device_info['name']} ({device_info['maxOutputChannels']} ch)")
            except Exception:
                continue
    
    def _find_best_output_device(self) -> int:
        """Find the best available output device"""
        try:
            # Try to find default output device
            default_device = self.pa.get_default_output_device_info()
            device_index = default_device['index']
            
            logger.info(f"Using default output device: {default_device['name']}")
            return device_index
            
        except Exception:
            # Fallback to first available output device
            device_count = self.pa.get_device_count()
            for i in range(device_count):
                try:
                    device_info = self.pa.get_device_info_by_index(i)
                    if device_info['maxOutputChannels'] > 0:
                        logger.info(f" Using fallback output device: {device_info['name']}")
                        return i
                except Exception:
                    continue
            
            logger.error(" No output devices found")
            return 0
    
    def _create_output_stream(self):
        """Create PyAudio output stream"""
        try:
            self.output_stream = self.pa.open(
                format=pyaudio.paFloat32,
                channels=self.channels,
                rate=self.sample_rate,
                output=True,
                output_device_index=self.device_index,
                frames_per_buffer=self.chunk_size,
                stream_callback=self._audio_callback,
                start=False  # We'll start manually
            )
            
        except Exception as e:
            logger.error(f" Failed to create output stream: {e}")
            raise
    
    def _audio_callback(self, in_data, frame_count, time_info, status):
        """
        Real-time audio callback - CRITICAL PATH
        This runs in audio thread with minimal processing
        """
        callback_start = time.time()
        
        try:
            # DEBUG: Log callback activity
            if hasattr(self, '_callback_count'):
                self._callback_count += 1
            else:
                self._callback_count = 1
                logger.info(f" Audio callback started - requesting {frame_count} samples")

            # Log every 10 callbacks to monitor activity
            if self._callback_count % 10 == 0:
                logger.info(f" Audio callback #{self._callback_count} - buffer available: {self.playback_buffer.available_samples}")

            # Check for interruption
            if self.is_interrupted:
                self._handle_interruption()
                silence = np.zeros(frame_count, dtype=np.float32)
                return (silence.tobytes(), pyaudio.paContinue)

            # Get audio data from buffer
            audio_data = self.playback_buffer.get_audio_chunk(frame_count)

            # DEBUG: Log buffer consumption every callback for debugging
            if self._callback_count <= 5 or self._callback_count % 10 == 0:  # Log first 5 and every 10th
                logger.info(f" Audio callback #{self._callback_count} - got {len(audio_data) if audio_data is not None else 0} samples, buffer has {self.playback_buffer.available_samples}")

            if audio_data is not None and len(audio_data) > 0:
                # Apply volume and effects
                audio_data = self._apply_audio_effects(audio_data)
                
                # Ensure correct length
                if len(audio_data) != frame_count:
                    if len(audio_data) < frame_count:
                        # Pad with zeros
                        padding = np.zeros(frame_count - len(audio_data), dtype=np.float32)
                        audio_data = np.concatenate([audio_data, padding])
                    else:
                        # Truncate
                        audio_data = audio_data[:frame_count]
                
                self.is_playing = True
                self.audio_chunks_played += 1
                self.total_audio_played += len(audio_data) / self.sample_rate
                
            else:
                # No audio available - output silence
                audio_data = np.zeros(frame_count, dtype=np.float32)
                if self.is_playing:
                    # Track underrun
                    self.underruns += 1
                    logger.debug(" Audio underrun - no data available")
                
                self.is_playing = False
            
            # Performance tracking
            callback_time = time.time() - callback_start
            self.callback_times.append(callback_time)
            if len(self.callback_times) > 1000:
                self.callback_times = self.callback_times[-1000:]
            
            return (audio_data.tobytes(), pyaudio.paContinue)
            
        except Exception as e:
            logger.error(f" Audio callback error: {e}")
            silence = np.zeros(frame_count, dtype=np.float32)
            return (silence.tobytes(), pyaudio.paContinue)
    
    def _apply_audio_effects(self, audio_data: np.ndarray) -> np.ndarray:
        """Apply volume and other audio effects"""
        try:
            # Apply mute
            if self.muted:
                return np.zeros_like(audio_data)
            
            # Apply volume
            if self.volume != 1.0:
                audio_data = audio_data * self.volume
            
            # Clip to prevent distortion
            audio_data = np.clip(audio_data, -1.0, 1.0)
            
            return audio_data
            
        except Exception as e:
            logger.error(f" Audio effects error: {e}")
            return audio_data
    
    def _handle_interruption(self):
        """Handle audio interruption with smooth fade"""
        try:
            # Clear buffer
            self.playback_buffer.clear()
            
            # Reset interruption flag
            self.is_interrupted = False
            
            logger.debug(" Audio interruption handled")
            
        except Exception as e:
            logger.error(f" Interruption handling error: {e}")
    
    def start_audio_stream(self) -> bool:
        """Start the audio output stream"""
        if not self.output_stream:
            logger.error(" Audio stream not initialized")
            return False
        
        try:
            self.output_stream.start_stream()
            logger.info("Audio output stream started")
            return True
        except Exception as e:
            logger.error(f" Failed to start audio stream: {e}")
            return False
    
    def stop_audio_stream(self):
        """Stop the audio output stream"""
        if self.output_stream and self.output_stream.is_active():
            try:
                self.output_stream.stop_stream()
                logger.info(" Audio output stream stopped")
            except Exception as e:
                logger.error(f" Error stopping audio stream: {e}")
    
    def interrupt_playback(self):
        """Interrupt current playback immediately"""
        logger.debug(" Interrupting audio playback")
        self.is_interrupted = True
    
    def set_volume(self, volume: float):
        """Set playback volume (0.0 to 1.0)"""
        self.volume = max(0.0, min(1.0, volume))
        logger.debug(f" Volume set to {self.volume:.2f}")
    
    def set_mute(self, muted: bool):
        """Set mute state"""
        self.muted = muted
        logger.debug(f" Mute: {'ON' if muted else 'OFF'}")

    def run_processing_loop(self):
        """Main processing loop (required by HighPriorityThread)"""
        self.process_loop()

    def process_loop(self):
        """Main audio output processing loop"""
        if not self.initialize_audio_output():
            logger.error(" Failed to initialize audio output")
            return
        
        if not self.start_audio_stream():
            logger.error(" Failed to start audio stream")
            return
        
        logger.info("Audio output processor started")
        self.running.set()  # Set running flag

        try:
            while self.running.is_set() and not self.shutdown_event.is_set():
                self._process_audio_input()
                
        except Exception as e:
            logger.error(f" Audio output processing error: {e}")
        finally:
            self._cleanup_audio_system()
    
    def _process_audio_input(self):
        """Process audio input from TTS"""
        # Get audio from TTS
        tts_result = self.input_queue.get_nowait()
        if tts_result is None:
            time.sleep(0.001)  # 1ms sleep when no data
            return

        # DEBUG: Log that we received TTS data
        logger.info(f" AUDIO_OUTPUT DEBUG: Received TTS result from queue")
        
        try:
            # Extract audio data
            audio_data = tts_result.data
            logger.info(f" AUDIO_OUTPUT DEBUG: TTS result data type: {type(audio_data)}")

            if not isinstance(audio_data, dict) or 'audio_data' not in audio_data:
                logger.warning(f" Invalid audio data from TTS: {type(audio_data)}, keys: {audio_data.keys() if isinstance(audio_data, dict) else 'N/A'}")
                return

            audio_samples = audio_data['audio_data']
            logger.info(f" AUDIO_OUTPUT DEBUG: Audio samples type: {type(audio_samples)}, length: {len(audio_samples) if hasattr(audio_samples, '__len__') else 'N/A'}")

            if not isinstance(audio_samples, np.ndarray):
                logger.warning(" Audio data is not numpy array")
                return
            
            # Validate audio format
            if not self._validate_audio_format(audio_samples, audio_data):
                return
            
            # Add to playback buffer
            success = self.playback_buffer.add_audio(
                audio_samples,
                audio_data.get('chunk_index', 0),
                audio_data.get('total_chunks', 1)
            )
            
            if success:
                self.current_audio_info = audio_data
                logger.debug(f" Audio queued: {len(audio_samples)} samples, {audio_data.get('duration', 0):.2f}s")
            else:
                self.overruns += 1
                logger.warning(" Audio buffer full - dropping audio")
            
        except Exception as e:
            logger.error(f" Error processing audio input: {e}")
    
    def _validate_audio_format(self, audio_samples: np.ndarray, audio_info: Dict) -> bool:
        """Validate audio format matches output requirements"""
        try:
            # Check data type
            if audio_samples.dtype != np.float32:
                logger.warning(f" Converting audio from {audio_samples.dtype} to float32")
                audio_samples = audio_samples.astype(np.float32)
            
            # Check sample rate
            source_sample_rate = audio_info.get('sample_rate', self.sample_rate)
            if source_sample_rate != self.sample_rate:
                logger.warning(f" Sample rate mismatch: {source_sample_rate} vs {self.sample_rate}")
                # Note: Resampling should be handled in TTS processor
            
            # Check for valid audio data
            if len(audio_samples) == 0:
                logger.warning(" Empty audio data")
                return False
            
            # Check for audio clipping
            max_amplitude = np.max(np.abs(audio_samples))
            if max_amplitude > 1.0:
                logger.warning(f" Audio clipping detected: max amplitude {max_amplitude:.2f}")
                audio_samples = np.clip(audio_samples, -1.0, 1.0)
            
            return True
            
        except Exception as e:
            logger.error(f" Audio validation error: {e}")
            return False
    
    def _cleanup_audio_system(self):
        """Clean up audio resources"""
        logger.info(" Cleaning up audio output system...")
        
        self.stop_audio_stream()
        
        if self.output_stream:
            try:
                self.output_stream.close()
            except Exception as e:
                logger.error(f"Error closing stream: {e}")
        
        if self.pa:
            try:
                self.pa.terminate()
            except Exception as e:
                logger.error(f"Error terminating PyAudio: {e}")
        
        logger.info(" Audio output cleanup completed")
    
    def get_audio_output_stats(self) -> Dict[str, Any]:
        """Get detailed audio output statistics"""
        stats = self.get_performance_report()
        
        # Calculate buffer status
        buffer_status = self.playback_buffer.get_status()
        
        # Calculate callback performance
        if len(self.callback_times) > 0:
            avg_callback_time = np.mean(self.callback_times) * 1000  # Convert to ms
            max_callback_time = np.max(self.callback_times) * 1000
            callback_jitter = np.std(self.callback_times) * 1000
        else:
            avg_callback_time = max_callback_time = callback_jitter = 0.0
        
        stats.update({
            'sample_rate': self.sample_rate,
            'channels': self.channels,
            'chunk_size': self.chunk_size,
            'device_index': self.device_index,
            'is_playing': self.is_playing,
            'volume': self.volume,
            'muted': self.muted,
            'audio_chunks_played': self.audio_chunks_played,
            'total_audio_played_seconds': self.total_audio_played,
            'underruns': self.underruns,
            'overruns': self.overruns,
            'avg_callback_time_ms': avg_callback_time,
            'max_callback_time_ms': max_callback_time,
            'callback_jitter_ms': callback_jitter,
            'buffer_status': buffer_status,
            'queue_input_stats': self.input_queue.get_stats()
        })
        
        return stats

class PlaybackBuffer:
    """
    Smart audio buffer for smooth playback
    - Handles variable-length audio chunks
    - Prevents buffer under/overflow
    - Supports interruption and clearing
    """
    
    def __init__(self, sample_rate: int = 48000, max_duration: float = 3.0):
        self.sample_rate = sample_rate
        self.max_duration = max_duration
        self.max_samples = int(sample_rate * max_duration)
        
        # Ring buffer for audio data
        self.buffer = np.zeros(self.max_samples, dtype=np.float32)
        self.write_pos = 0
        self.read_pos = 0
        self.available_samples = 0
        
        # Thread safety
        self.buffer_lock = threading.Lock()
        
        # Chunk tracking
        self.chunks_added = 0
        self.chunks_played = 0
        
    def add_audio(self, audio_data: np.ndarray, chunk_index: int = 0, total_chunks: int = 1) -> bool:
        """Add audio data to the buffer"""
        with self.buffer_lock:
            audio_length = len(audio_data)

            # DEBUG: Log buffer state before adding
            logger.info(f" BUFFER DEBUG: Adding {audio_length} samples, buffer has {self.available_samples}/{self.max_samples} samples")

            # Check if buffer has space
            free_samples = self.max_samples - self.available_samples
            if audio_length > free_samples:
                # Buffer full
                logger.warning(f" BUFFER DEBUG: Buffer full! Need {audio_length}, only {free_samples} free")
                return False
            
            # Add audio to ring buffer
            if self.write_pos + audio_length <= self.max_samples:
                # No wraparound
                self.buffer[self.write_pos:self.write_pos + audio_length] = audio_data
            else:
                # Handle wraparound
                first_part = self.max_samples - self.write_pos
                self.buffer[self.write_pos:] = audio_data[:first_part]
                self.buffer[:audio_length - first_part] = audio_data[first_part:]
            
            # Update positions
            self.write_pos = (self.write_pos + audio_length) % self.max_samples
            self.available_samples += audio_length
            self.chunks_added += 1

            # DEBUG: Log successful addition
            logger.info(f" BUFFER DEBUG: Successfully added {audio_length} samples, buffer now has {self.available_samples}/{self.max_samples}")

            return True
    
    def get_audio_chunk(self, requested_samples: int) -> Optional[np.ndarray]:
        """Get audio chunk for playback"""
        with self.buffer_lock:
            if self.available_samples == 0:
                return None
            
            # Determine how many samples to return
            samples_to_return = min(requested_samples, self.available_samples)
            
            # Extract audio from ring buffer
            if self.read_pos + samples_to_return <= self.max_samples:
                # No wraparound
                audio_chunk = self.buffer[self.read_pos:self.read_pos + samples_to_return].copy()
            else:
                # Handle wraparound
                first_part = self.max_samples - self.read_pos
                audio_chunk = np.concatenate([
                    self.buffer[self.read_pos:],
                    self.buffer[:samples_to_return - first_part]
                ])
            
            # Update positions
            self.read_pos = (self.read_pos + samples_to_return) % self.max_samples
            self.available_samples -= samples_to_return
            
            if samples_to_return == requested_samples:
                self.chunks_played += 1
            
            return audio_chunk
    
    def clear(self):
        """Clear the buffer"""
        with self.buffer_lock:
            self.buffer.fill(0.0)
            self.write_pos = 0
            self.read_pos = 0
            self.available_samples = 0
    
    def get_status(self) -> Dict[str, Any]:
        """Get buffer status information"""
        with self.buffer_lock:
            usage_percent = (self.available_samples / self.max_samples) * 100
            duration_buffered = self.available_samples / self.sample_rate
            
            return {
                'available_samples': self.available_samples,
                'max_samples': self.max_samples,
                'usage_percent': usage_percent,
                'duration_buffered_seconds': duration_buffered,
                'chunks_added': self.chunks_added,
                'chunks_played': self.chunks_played,
                'write_pos': self.write_pos,
                'read_pos': self.read_pos
            }

# Test function
def test_audio_output():
    """Test audio output functionality"""
    print(" Testing Audio Output...")
    
    if not PYAUDIO_AVAILABLE:
        print(" PyAudio not available - cannot test audio output")
        return
    
    # Create test queue
    input_queue = LockFreeQueue(maxsize=100, name="AudioOutputTest")
    
    # Create audio output thread
    audio_output = AudioOutputThread(
        input_queue=input_queue,
        sample_rate=48000,
        chunk_size=768
    )
    
    try:
        # Start audio output
        audio_output.start()
        
        # Wait for initialization
        time.sleep(2)
        
        # Generate test audio (sine waves)
        print(" Playing test audio...")
        
        sample_rate = 48000
        duration = 2.0  # 2 seconds
        frequency = 440  # A4 note
        
        # Generate sine wave
        t = np.linspace(0, duration, int(sample_rate * duration))
        test_audio = (np.sin(2 * np.pi * frequency * t) * 0.1).astype(np.float32)
        
        # Split into chunks for streaming
        chunk_size = 4800  # 100ms chunks
        audio_chunks = [test_audio[i:i+chunk_size] for i in range(0, len(test_audio), chunk_size)]
        
        for i, chunk in enumerate(audio_chunks):
            # Create TTS result
            audio_result = {
                'audio_data': chunk,
                'sample_rate': sample_rate,
                'text': f"Test chunk {i+1}",
                'duration': len(chunk) / sample_rate,
                'chunk_index': i,
                'total_chunks': len(audio_chunks),
                'tts_engine': 'test'
            }
            
            tts_result = ProcessingResult(
                data=audio_result,
                timestamp=time.time(),
                latency=0.001,
                confidence=1.0,
                stage="TTS",
                chunk_id=i
            )
            
            input_queue.put_nowait(tts_result)
            time.sleep(0.05)  # 50ms delay between chunks
        
        # Wait for playback to complete
        print(" Waiting for playback to complete...")
        time.sleep(duration + 1.0)
        
        print(f" Audio output test completed")
        print(f" Audio stats: {audio_output.get_audio_output_stats()}")
        
    finally:
        audio_output.shutdown()
        audio_output.join(timeout=3.0)

if __name__ == "__main__":
    test_audio_output()