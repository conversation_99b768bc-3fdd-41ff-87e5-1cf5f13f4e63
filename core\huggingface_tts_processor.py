"""
Hugging Face TTS Processor
High-quality text-to-speech using Hugging Face models with GPU acceleration
"""

import numpy as np
import time
import logging
from typing import Optional, Dict, Any, List
import threading
import tempfile
import os
import torch
import warnings

# Suppress warnings for cleaner output
warnings.filterwarnings("ignore", category=UserWarning)
warnings.filterwarnings("ignore", category=FutureWarning)

try:
    from transformers import SpeechT5Processor, SpeechT5ForTextToSpeech, SpeechT5HifiGan
    from transformers import VitsModel, VitsTokenizer
    from datasets import load_dataset
    import soundfile as sf
    HUGGINGFACE_AVAILABLE = True
except ImportError:
    HUGGINGFACE_AVAILABLE = False

# Note: Coqui TTS has Python version constraints, so we'll focus on HF models
COQUI_TTS_AVAILABLE = False

try:
    import scipy.signal
    SCIPY_AVAILABLE = True
except ImportError:
    SCIPY_AVAILABLE = False

from .threading_infrastructure import (
    HighPriorityThread,
    LockFreeQueue,
    ProcessingResult
)

logger = logging.getLogger(__name__)


class HuggingFaceTTSThread(HighPriorityThread):
    """High-quality TTS processing thread using Hugging Face models"""
    
    def __init__(self,
                 input_queue: LockFreeQueue,
                 output_queue: LockFreeQueue,
                 model_name: str = "microsoft/speecht5_tts",
                 device: str = "cuda",
                 voice_preset: str = "female",
                 speech_rate: float = 1.0,
                 volume: float = 0.9):
        
        super().__init__("HF_TTS", priority="high", target_fps=10)
        
        # Queues
        self.input_queue = input_queue
        self.output_queue = output_queue
        
        # Model configuration
        self.model_name = model_name
        self.device = device if torch.cuda.is_available() else "cpu"
        self.voice_preset = voice_preset
        self.speech_rate = speech_rate
        self.volume = volume
        
        # Model components
        self.processor = None
        self.model = None
        self.vocoder = None
        self.speaker_embeddings = None
        self.tts_model = None  # For Coqui TTS
        
        # Performance tracking
        self.syntheses_processed = 0
        self.total_synthesis_time = 0
        self.failed_syntheses = 0
        self.total_text_length = 0
        
        # Audio settings
        self.model_sample_rate = 16000  # Default for most HF models
        self.output_sample_rate = 48000  # Target output sample rate for audio system

        logger.info(f"HF_TTS: Initialized (model: {model_name}, device: {self.device})")

    def _resample_audio(self, audio_data: np.ndarray, source_rate: int, target_rate: int) -> np.ndarray:
        """Resample audio from source rate to target rate"""
        if source_rate == target_rate:
            return audio_data

        if not SCIPY_AVAILABLE:
            logger.warning("HF_TTS: scipy not available, cannot resample audio")
            return audio_data

        try:
            # Calculate resampling ratio
            ratio = target_rate / source_rate

            # Use scipy's resample function for high-quality resampling
            resampled_audio = scipy.signal.resample(audio_data, int(len(audio_data) * ratio))

            logger.info(f"HF_TTS: Resampled audio from {source_rate}Hz to {target_rate}Hz ({len(audio_data)} -> {len(resampled_audio)} samples)")
            return resampled_audio.astype(np.float32)

        except Exception as e:
            logger.error(f"HF_TTS: Failed to resample audio: {e}")
            return audio_data

    def initialize(self) -> bool:
        """Initialize Hugging Face TTS system"""
        try:
            if not HUGGINGFACE_AVAILABLE:
                logger.error("FATAL: Hugging Face transformers not available")
                logger.error("Install with: pip install transformers datasets soundfile")
                return False
            
            logger.info(f"HF_TTS: Loading model {self.model_name} on {self.device}...")
            
            if "speecht5" in self.model_name.lower():
                return self._initialize_speecht5()
            elif "vits" in self.model_name.lower() or "mms-tts" in self.model_name.lower():
                return self._initialize_vits()
            else:
                logger.error(f"ERROR: Unsupported model type: {self.model_name}")
                logger.error("Supported models: microsoft/speecht5_tts, facebook/mms-tts-*, *vits*")
                return False
                
        except Exception as e:
            logger.error(f"FATAL: HF TTS initialization failed: {e}")
            return False
    
    def _initialize_speecht5(self) -> bool:
        """Initialize SpeechT5 model"""
        try:
            # Load processor and model
            self.processor = SpeechT5Processor.from_pretrained(self.model_name)
            self.model = SpeechT5ForTextToSpeech.from_pretrained(self.model_name)
            self.vocoder = SpeechT5HifiGan.from_pretrained("microsoft/speecht5_hifigan")
            
            # Move to device
            self.model = self.model.to(self.device)
            self.vocoder = self.vocoder.to(self.device)
            
            # Load speaker embeddings
            self._load_speaker_embeddings()
            
            self.model_sample_rate = 16000  # SpeechT5 uses 16kHz
            
            logger.info("HF_TTS: SpeechT5 model loaded successfully")
            return True
            
        except Exception as e:
            logger.error(f"ERROR: SpeechT5 initialization failed: {e}")
            return False
    
    def _initialize_vits(self) -> bool:
        """Initialize VITS model"""
        try:
            # Try different model classes for different VITS variants
            if "mms-tts" in self.model_name.lower():
                # MMS TTS models use a different interface
                from transformers import VitsModel, VitsTokenizer
                self.model = VitsModel.from_pretrained(self.model_name)
                self.processor = VitsTokenizer.from_pretrained(self.model_name)
            else:
                # Standard VITS models
                from transformers import VitsModel, VitsTokenizer
                self.model = VitsModel.from_pretrained(self.model_name)
                self.processor = VitsTokenizer.from_pretrained(self.model_name)

            # Move to device
            self.model = self.model.to(self.device)

            # VITS typically uses 22kHz
            self.model_sample_rate = getattr(self.model.config, 'sampling_rate', 22050)

            logger.info("HF_TTS: VITS model loaded successfully")
            return True

        except Exception as e:
            logger.error(f"ERROR: VITS initialization failed: {e}")
            return False
    
    # Coqui TTS initialization removed due to Python version constraints
    
    def _load_speaker_embeddings(self):
        """Load speaker embeddings for SpeechT5"""
        try:
            # Load speaker embeddings dataset
            embeddings_dataset = load_dataset("Matthijs/cmu-arctic-xvectors", split="validation")
            
            # Select speaker based on voice preset
            if self.voice_preset.lower() in ["female", "woman"]:
                # Use a female speaker (e.g., speaker 7306)
                speaker_id = 7306
            else:
                # Use a male speaker (e.g., speaker 6097)
                speaker_id = 6097
            
            # Find the speaker in the dataset
            speaker_embedding = None
            for item in embeddings_dataset:
                if item["speaker"] == speaker_id:
                    speaker_embedding = item["xvector"]
                    break
            
            if speaker_embedding is not None:
                self.speaker_embeddings = torch.tensor(speaker_embedding).unsqueeze(0).to(self.device)
                logger.info(f"HF_TTS: Loaded speaker embeddings for speaker {speaker_id}")
            else:
                # Use first available speaker as fallback
                first_item = embeddings_dataset[0]
                self.speaker_embeddings = torch.tensor(first_item["xvector"]).unsqueeze(0).to(self.device)
                logger.warning("HF_TTS: Using fallback speaker embeddings")
                
        except Exception as e:
            logger.error(f"ERROR: Failed to load speaker embeddings: {e}")
            # Create dummy embeddings as last resort
            self.speaker_embeddings = torch.randn(1, 512).to(self.device)
    
    def run_processing_loop(self):
        """Main TTS processing loop"""
        logger.info("HF_TTS: Processing loop started")
        
        while not self.should_stop:
            try:
                # Get AI response text
                ai_result = self.input_queue.get_nowait()
                if ai_result is None:
                    time.sleep(0.01)  # Brief pause if no data
                    continue

                logger.info(f"HF_TTS DEBUG: Received AI response for synthesis")
                
                # Process text for speech synthesis
                start_time = time.time()
                tts_result = self._synthesize_speech(ai_result)
                processing_time = time.time() - start_time
                
                # Send result if synthesis successful
                if tts_result:
                    logger.info(f"HF_TTS DEBUG: Generated audio, sending to output")
                    success = self.output_queue.put_nowait(tts_result)
                    if not success:
                        logger.warning("WARNING: HF TTS output queue full")
                
                # Update performance stats
                self.update_performance_stats(processing_time)
                
            except Exception as e:
                logger.error(f"ERROR: HF TTS processing error: {e}")
                time.sleep(0.01)
    
    def _synthesize_speech(self, ai_result: ProcessingResult) -> Optional[ProcessingResult]:
        """Synthesize speech from text using Hugging Face models"""
        try:
            text = ai_result.data['text']
            
            # Skip empty text
            if not text or not text.strip():
                logger.debug("HF_TTS: Skipping empty text")
                return None
            
            # Clean text for TTS
            cleaned_text = self._clean_text_for_tts(text)
            
            if not cleaned_text:
                logger.debug("HF_TTS: No text after cleaning")
                return None
            
            # Synthesize speech
            start_time = time.time()
            audio_data = self._generate_speech_audio(cleaned_text)
            synthesis_time = time.time() - start_time
            
            if audio_data is not None:
                result = ProcessingResult(
                    data={
                        'audio_data': audio_data,
                        'sample_rate': self.output_sample_rate,
                        'text': cleaned_text,
                        'original_text': text,
                        'synthesis_time': synthesis_time,
                        'tts_engine': 'huggingface',
                        'model_name': self.model_name,
                        'device': self.device
                    },
                    timestamp=time.time(),
                    processing_time_ms=synthesis_time * 1000,
                    metadata={
                        'source': 'hf_tts',
                        'text_length': len(cleaned_text),
                        'audio_duration': len(audio_data) / self.output_sample_rate if len(audio_data) > 0 else 0
                    }
                )
                
                self.syntheses_processed += 1
                self.total_synthesis_time += synthesis_time
                self.total_text_length += len(cleaned_text)
                
                # Remove emojis and special characters for logging to prevent Unicode errors
                safe_text = ''.join(c for c in cleaned_text[:50] if ord(c) < 128)
                logger.info(f"HF_TTS: '{safe_text}...' -> {len(audio_data)} samples ({synthesis_time:.2f}s)")
                return result
            else:
                self.failed_syntheses += 1
                logger.error("ERROR: HF speech synthesis failed")
                return None
                
        except Exception as e:
            logger.error(f"ERROR: HF speech synthesis error: {e}")
            self.failed_syntheses += 1
            return None

    def _clean_text_for_tts(self, text: str) -> str:
        """Clean text for TTS synthesis"""
        try:
            # Remove extra whitespace
            cleaned = ' '.join(text.split())

            # Remove or replace problematic characters
            replacements = {
                '"': '',
                '"': '',
                '"': '',
                ''': "'",
                ''': "'",
                '…': '...',
                '–': '-',
                '—': '-',
                '\n': ' ',
                '\r': ' ',
                '\t': ' '
            }

            for old, new in replacements.items():
                cleaned = cleaned.replace(old, new)

            # Remove multiple spaces
            while '  ' in cleaned:
                cleaned = cleaned.replace('  ', ' ')

            return cleaned.strip()

        except Exception as e:
            logger.error(f"ERROR: Text cleaning failed: {e}")
            return text

    def _generate_speech_audio(self, text: str) -> Optional[np.ndarray]:
        """Generate speech audio from text"""
        try:
            # Remove emojis and special characters for logging to prevent Unicode errors
            safe_text = ''.join(c for c in text[:50] if ord(c) < 128)
            logger.info(f"HF_TTS DEBUG: Generating audio for model '{self.model_name}' with text: '{safe_text}...'")
            if "speecht5" in self.model_name.lower():
                return self._generate_speecht5_audio(text)
            elif "vits" in self.model_name.lower() or "mms-tts" in self.model_name.lower():
                return self._generate_vits_audio(text)
            else:
                logger.error(f"ERROR: No valid TTS model available for {self.model_name}")
                return None

        except Exception as e:
            logger.error(f"ERROR: Audio generation failed: {e}")
            return None

    def _generate_speecht5_audio(self, text: str) -> Optional[np.ndarray]:
        """Generate audio using SpeechT5"""
        try:
            # Tokenize text
            inputs = self.processor(text=text, return_tensors="pt")
            input_ids = inputs["input_ids"].to(self.device)

            # Generate speech
            with torch.no_grad():
                speech = self.model.generate_speech(
                    input_ids,
                    self.speaker_embeddings,
                    vocoder=self.vocoder
                )

            # Convert to numpy and apply post-processing
            audio_data = speech.cpu().numpy()

            # Apply volume scaling
            audio_data = audio_data * self.volume

            # Apply speech rate adjustment (simple time-stretching)
            if self.speech_rate != 1.0:
                audio_data = self._adjust_speech_rate(audio_data, self.speech_rate)

            # Resample to target output sample rate
            logger.info(f"HF_TTS DEBUG: About to resample SpeechT5 audio from {self.model_sample_rate}Hz to {self.output_sample_rate}Hz")
            audio_data = self._resample_audio(audio_data, self.model_sample_rate, self.output_sample_rate)

            return audio_data.astype(np.float32)

        except Exception as e:
            logger.error(f"ERROR: SpeechT5 audio generation failed: {e}")
            return None

    def _generate_vits_audio(self, text: str) -> Optional[np.ndarray]:
        """Generate audio using VITS"""
        try:
            # Tokenize text
            inputs = self.processor(text, return_tensors="pt")

            # Move inputs to device
            for key in inputs:
                if hasattr(inputs[key], 'to'):
                    inputs[key] = inputs[key].to(self.device)

            # Generate speech
            with torch.no_grad():
                outputs = self.model(**inputs)

                # Handle different output formats
                if hasattr(outputs, 'waveform'):
                    audio_data = outputs.waveform.squeeze().cpu().numpy()
                elif hasattr(outputs, 'audio'):
                    audio_data = outputs.audio.squeeze().cpu().numpy()
                elif hasattr(outputs, 'last_hidden_state'):
                    # Some models return hidden states that need vocoding
                    audio_data = outputs.last_hidden_state.squeeze().cpu().numpy()
                else:
                    # Try to get the first tensor output
                    audio_data = list(outputs.values())[0].squeeze().cpu().numpy()

            # Ensure we have a 1D array
            if len(audio_data.shape) > 1:
                audio_data = audio_data.flatten()

            # Apply volume scaling
            audio_data = audio_data * self.volume

            # Apply speech rate adjustment
            if self.speech_rate != 1.0:
                audio_data = self._adjust_speech_rate(audio_data, self.speech_rate)

            # Resample to target output sample rate
            logger.info(f"HF_TTS DEBUG: About to resample VITS audio from {self.model_sample_rate}Hz to {self.output_sample_rate}Hz")
            audio_data = self._resample_audio(audio_data, self.model_sample_rate, self.output_sample_rate)

            return audio_data.astype(np.float32)

        except Exception as e:
            logger.error(f"ERROR: VITS audio generation failed: {e}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return None

    # Coqui TTS audio generation removed due to Python version constraints

    def _adjust_speech_rate(self, audio_data: np.ndarray, rate: float) -> np.ndarray:
        """Adjust speech rate using simple resampling"""
        try:
            if rate == 1.0:
                return audio_data

            # Simple time-stretching by resampling
            new_length = int(len(audio_data) / rate)
            indices = np.linspace(0, len(audio_data) - 1, new_length)
            return np.interp(indices, np.arange(len(audio_data)), audio_data)

        except Exception as e:
            logger.error(f"ERROR: Speech rate adjustment failed: {e}")
            return audio_data

    def get_tts_stats(self) -> Dict[str, Any]:
        """Get TTS statistics"""
        avg_synthesis_time = 0
        avg_text_length = 0
        chars_per_second = 0

        if self.syntheses_processed > 0:
            avg_synthesis_time = self.total_synthesis_time / self.syntheses_processed
            avg_text_length = self.total_text_length / self.syntheses_processed

            if self.total_synthesis_time > 0:
                chars_per_second = self.total_text_length / self.total_synthesis_time

        return {
            'tts_engine': 'huggingface',
            'model_name': self.model_name,
            'device': self.device,
            'voice_preset': self.voice_preset,
            'speech_rate': self.speech_rate,
            'volume': self.volume,
            'syntheses_processed': self.syntheses_processed,
            'failed_syntheses': self.failed_syntheses,
            'success_rate': self.syntheses_processed / max(1, self.syntheses_processed + self.failed_syntheses),
            'avg_synthesis_time': avg_synthesis_time,
            'avg_text_length': avg_text_length,
            'chars_per_second': chars_per_second,
            'sample_rate': self.output_sample_rate,
            'cuda_available': torch.cuda.is_available(),
            'gpu_memory_used': torch.cuda.memory_allocated() / 1024**2 if torch.cuda.is_available() else 0
        }

    def cleanup(self):
        """Cleanup TTS resources"""
        try:
            # Clear CUDA cache
            if torch.cuda.is_available():
                torch.cuda.empty_cache()

            # Clean up models
            if self.model:
                del self.model
                self.model = None

            if self.processor:
                del self.processor
                self.processor = None

            if self.vocoder:
                del self.vocoder
                self.vocoder = None

            if self.speaker_embeddings is not None:
                del self.speaker_embeddings
                self.speaker_embeddings = None

            if self.tts_model:
                del self.tts_model
                self.tts_model = None

            logger.debug("HF_TTS: Resources cleaned up")

        except Exception as e:
            logger.error(f"ERROR: HF TTS cleanup error: {e}")


# Test function
def test_huggingface_tts():
    """Test Hugging Face TTS functionality"""
    print("Testing Hugging Face TTS...")

    if not HUGGINGFACE_AVAILABLE:
        print("ERROR: Hugging Face transformers not available")
        return

    # Create test queues
    from .threading_infrastructure import LockFreeQueue
    input_queue = LockFreeQueue(maxsize=100, name="HFTTSInputTest")
    output_queue = LockFreeQueue(maxsize=100, name="HFTTSOutputTest")

    # Create HF TTS thread
    tts = HuggingFaceTTSThread(
        input_queue=input_queue,
        output_queue=output_queue,
        model_name="microsoft/speecht5_tts",
        device="cuda" if torch.cuda.is_available() else "cpu"
    )

    # Test initialization
    if tts.initialize():
        print("SUCCESS: Hugging Face TTS initialized")

        # Test text cleaning
        test_text = "Hello, this is a test of the Hugging Face TTS system!"
        cleaned = tts._clean_text_for_tts(test_text)
        print(f"Text cleaned: '{test_text}' -> '{cleaned}'")

        tts.cleanup()
        print("SUCCESS: Hugging Face TTS test completed")
    else:
        print("ERROR: Hugging Face TTS initialization failed")


if __name__ == "__main__":
    test_huggingface_tts()
